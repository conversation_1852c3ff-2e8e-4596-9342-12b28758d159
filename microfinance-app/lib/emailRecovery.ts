import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Email log interface
export interface EmailLogEntry {
  emailType: 'monthly' | 'weekly';
  period: string;
  sentDate: Date;
  status: 'sent' | 'failed' | 'recovered';
  recipients: string[];
  fileName?: string;
  isRecovery?: boolean;
  errorMessage?: string;
}

// Function to log email send
export async function logEmailSend(entry: EmailLogEntry): Promise<void> {
  try {
    await prisma.emailLog.upsert({
      where: {
        emailType_period: {
          emailType: entry.emailType,
          period: entry.period
        }
      },
      update: {
        sentDate: entry.sentDate,
        status: entry.status,
        recipients: JSON.stringify(entry.recipients),
        fileName: entry.fileName,
        isRecovery: entry.isRecovery || false,
        errorMessage: entry.errorMessage,
        updatedAt: new Date()
      },
      create: {
        emailType: entry.emailType,
        period: entry.period,
        sentDate: entry.sentDate,
        status: entry.status,
        recipients: JSON.stringify(entry.recipients),
        fileName: entry.fileName,
        isRecovery: entry.isRecovery || false,
        errorMessage: entry.errorMessage
      }
    });

    console.log(`Email log recorded: ${entry.emailType} for period ${entry.period}`);
  } catch (error) {
    console.error('Error logging email send:', error);
  }
}

// Function to get email logs
export async function getEmailLogs(emailType?: 'monthly' | 'weekly', limit: number = 50) {
  try {
    const logs = await prisma.emailLog.findMany({
      where: emailType ? { emailType } : undefined,
      orderBy: { sentDate: 'desc' },
      take: limit
    });

    return logs.map(log => ({
      ...log,
      recipients: JSON.parse(log.recipients)
    }));
  } catch (error) {
    console.error('Error fetching email logs:', error);
    return [];
  }
}

// Function to generate period string for monthly emails
export function getMonthlyPeriod(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  return `${year}-${month}`;
}

// Function to generate period string for weekly emails
export function getWeeklyPeriod(date: Date): string {
  const year = date.getFullYear();
  const weekNumber = getWeekNumber(date);
  return `${year}-W${String(weekNumber).padStart(2, '0')}`;
}

// Helper function to get week number
function getWeekNumber(date: Date): number {
  const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
  const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
  return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
}

// Function to calculate expected monthly email dates
export function getExpectedMonthlyEmailDates(startDate: Date, endDate: Date): Date[] {
  const dates: Date[] = [];
  const day = parseInt(process.env.AUTO_MONTHLY_EMAIL_DAY || '1', 10);
  const hour = parseInt(process.env.AUTO_MONTHLY_EMAIL_HOUR || '9', 10);
  const timezone = process.env.AUTO_MONTHLY_EMAIL_TIMEZONE || 'Asia/Kolkata';

  let current = new Date(startDate);
  current.setDate(day);
  current.setHours(hour, 0, 0, 0);

  // If the day has already passed in the start month, move to next month
  if (current < startDate) {
    current.setMonth(current.getMonth() + 1);
  }

  while (current <= endDate) {
    dates.push(new Date(current));
    current.setMonth(current.getMonth() + 1);
  }

  return dates;
}

// Function to calculate expected weekly email dates
export function getExpectedWeeklyEmailDates(startDate: Date, endDate: Date): Date[] {
  const dates: Date[] = [];
  const dayOfWeek = parseInt(process.env.AUTO_WEEKLY_EMAIL_DAY || '0', 10);
  const hour = parseInt(process.env.AUTO_WEEKLY_EMAIL_HOUR || '18', 10);

  let current = new Date(startDate);

  // Find the first occurrence of the target day of week
  while (current.getDay() !== dayOfWeek) {
    current.setDate(current.getDate() + 1);
  }

  current.setHours(hour, 0, 0, 0);

  // If this time has already passed, move to next week
  if (current < startDate) {
    current.setDate(current.getDate() + 7);
  }

  while (current <= endDate) {
    dates.push(new Date(current));
    current.setDate(current.getDate() + 7);
  }

  return dates;
}

// Function to find missed monthly emails
export async function findMissedMonthlyEmails(): Promise<string[]> {
  try {
    const isEnabled = process.env.AUTO_MONTHLY_EMAIL_ENABLED === 'true';
    if (!isEnabled) {
      return [];
    }

    // Get the date range to check (last 6 months to current)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 6);

    // Get expected email dates
    const expectedDates = getExpectedMonthlyEmailDates(startDate, endDate);

    // Get actual sent emails
    const sentLogs = await prisma.emailLog.findMany({
      where: {
        emailType: 'monthly',
        sentDate: {
          gte: startDate,
          lte: endDate
        }
      }
    });

    const sentPeriods = new Set(sentLogs.map(log => log.period));
    const missedPeriods: string[] = [];

    // Check each expected date
    for (const expectedDate of expectedDates) {
      // Only check dates that are in the past (with 1 hour buffer)
      const bufferDate = new Date();
      bufferDate.setHours(bufferDate.getHours() - 1);

      if (expectedDate <= bufferDate) {
        const period = getMonthlyPeriod(expectedDate);
        if (!sentPeriods.has(period)) {
          missedPeriods.push(period);
        }
      }
    }

    return missedPeriods;
  } catch (error) {
    console.error('Error finding missed monthly emails:', error);
    return [];
  }
}

// Function to find missed weekly emails
export async function findMissedWeeklyEmails(): Promise<string[]> {
  try {
    const isEnabled = process.env.AUTO_WEEKLY_EMAIL_ENABLED === 'true';
    if (!isEnabled) {
      return [];
    }

    // Get the date range to check (last 8 weeks to current)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - (8 * 7));

    // Get expected email dates
    const expectedDates = getExpectedWeeklyEmailDates(startDate, endDate);

    // Get actual sent emails
    const sentLogs = await prisma.emailLog.findMany({
      where: {
        emailType: 'weekly',
        sentDate: {
          gte: startDate,
          lte: endDate
        }
      }
    });

    const sentPeriods = new Set(sentLogs.map(log => log.period));
    const missedPeriods: string[] = [];

    // Check each expected date
    for (const expectedDate of expectedDates) {
      // Only check dates that are in the past (with 1 hour buffer)
      const bufferDate = new Date();
      bufferDate.setHours(bufferDate.getHours() - 1);

      if (expectedDate <= bufferDate) {
        const period = getWeeklyPeriod(expectedDate);
        if (!sentPeriods.has(period)) {
          missedPeriods.push(period);
        }
      }
    }

    return missedPeriods;
  } catch (error) {
    console.error('Error finding missed weekly emails:', error);
    return [];
  }
}

// Function to send recovery email for a specific monthly period
export async function sendRecoveryMonthlyEmail(period: string): Promise<boolean> {
  try {
    const baseUrl = process.env.NEXTAUTH_URL || process.env.VERCEL_URL || 'http://localhost:3002';
    const internalKey = process.env.INTERNAL_API_KEY || 'default-internal-key';

    console.log(`Sending recovery monthly email for period: ${period}`);

    const response = await fetch(`${baseUrl}/api/scheduled/email/recovery`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${internalKey}`
      },
      body: JSON.stringify({
        type: 'monthly',
        period: period
      })
    });

    if (response.ok) {
      const result = await response.json();
      console.log(`Recovery monthly email sent successfully for ${period}:`, result);
      return true;
    } else {
      const error = await response.text();
      console.error(`Failed to send recovery monthly email for ${period}:`, error);
      return false;
    }
  } catch (error) {
    console.error(`Error sending recovery monthly email for ${period}:`, error);
    return false;
  }
}

// Function to send recovery email for a specific weekly period
export async function sendRecoveryWeeklyEmail(period: string): Promise<boolean> {
  try {
    const baseUrl = process.env.NEXTAUTH_URL || process.env.VERCEL_URL || 'http://localhost:3002';
    const internalKey = process.env.INTERNAL_API_KEY || 'default-internal-key';

    console.log(`Sending recovery weekly email for period: ${period}`);

    const response = await fetch(`${baseUrl}/api/scheduled/weekly-email/recovery`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${internalKey}`
      },
      body: JSON.stringify({
        type: 'weekly',
        period: period
      })
    });

    if (response.ok) {
      const result = await response.json();
      console.log(`Recovery weekly email sent successfully for ${period}:`, result);
      return true;
    } else {
      const error = await response.text();
      console.error(`Failed to send recovery weekly email for ${period}:`, error);
      return false;
    }
  } catch (error) {
    console.error(`Error sending recovery weekly email for ${period}:`, error);
    return false;
  }
}

// Main function to check and send all missed emails
export async function checkAndSendMissedEmails(): Promise<void> {
  try {
    // Check if recovery is enabled
    const recoveryEnabled = process.env.EMAIL_RECOVERY_ENABLED !== 'false'; // Default to true
    if (!recoveryEnabled) {
      console.log('Email recovery is disabled');
      return;
    }

    console.log('Checking for missed emails...');

    // Find missed monthly emails
    const missedMonthly = await findMissedMonthlyEmails();
    if (missedMonthly.length > 0) {
      console.log(`Found ${missedMonthly.length} missed monthly emails:`, missedMonthly);

      for (const period of missedMonthly) {
        const success = await sendRecoveryMonthlyEmail(period);
        if (success) {
          console.log(`✓ Recovered monthly email for ${period}`);
        } else {
          console.log(`✗ Failed to recover monthly email for ${period}`);
        }

        // Add delay between recovery emails to avoid overwhelming the system
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    // Find missed weekly emails
    const missedWeekly = await findMissedWeeklyEmails();
    if (missedWeekly.length > 0) {
      console.log(`Found ${missedWeekly.length} missed weekly emails:`, missedWeekly);

      for (const period of missedWeekly) {
        const success = await sendRecoveryWeeklyEmail(period);
        if (success) {
          console.log(`✓ Recovered weekly email for ${period}`);
        } else {
          console.log(`✗ Failed to recover weekly email for ${period}`);
        }

        // Add delay between recovery emails
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    if (missedMonthly.length === 0 && missedWeekly.length === 0) {
      console.log('No missed emails found');
    }

  } catch (error) {
    console.error('Error checking and sending missed emails:', error);
  }
}
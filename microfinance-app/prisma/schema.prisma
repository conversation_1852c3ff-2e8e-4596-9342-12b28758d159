generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id            Int            @id @default(autoincrement())
  name          String
  email         String         @unique
  password      String
  role          String         @default("user")
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  chitFunds     ChitFund[]
  globalMembers GlobalMember[]
  loans         Loan[]
}

model ChitFund {
  id                     Int                @id @default(autoincrement())
  name                   String
  totalAmount            Float
  monthlyContribution    Float
  firstMonthContribution Float?             // For Fixed type chit funds
  duration               Int
  membersCount           Int
  status                 String             @default("Active")
  startDate              DateTime
  currentMonth           Int                @default(1)
  nextAuctionDate        DateTime?
  description            String?
  chitFundType           String             @default("Auction") // "Auction" or "Fixed"
  createdAt              DateTime           @default(now())
  updatedAt              DateTime           @updatedAt
  createdById            Int
  auctions            Auction[]
  createdBy           User               @relation(fields: [createdById], references: [id])
  contributions       Contribution[]
  members             Member[]
  fixedAmounts        ChitFundFixedAmount[]

  @@index([createdById], map: "ChitFund_createdById_fkey")
}

model GlobalMember {
  id              Int      @id @default(autoincrement())
  name            String
  contact         String
  email           String?
  address         String?
  notes           String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  createdById     Int
  createdBy       User     @relation(fields: [createdById], references: [id])
  loans           Loan[]   @relation("BorrowerToLoan")
  chitFundMembers Member[]

  @@index([createdById], map: "GlobalMember_createdById_fkey")
}

model Member {
  id             Int            @id @default(autoincrement())
  globalMemberId Int
  chitFundId     Int
  joinDate       DateTime       @default(now())
  auctionWon     Boolean        @default(false)
  auctionMonth   Int?
  contribution   Float
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  auctions       Auction[]
  contributions  Contribution[]
  chitFund       ChitFund       @relation(fields: [chitFundId], references: [id])
  globalMember   GlobalMember   @relation(fields: [globalMemberId], references: [id])

  @@index([chitFundId], map: "Member_chitFundId_fkey")
  @@index([globalMemberId], map: "Member_globalMemberId_fkey")
}

model Contribution {
  id                       Int       @id @default(autoincrement())
  amount                   Float
  month                    Int
  paidDate                 DateTime
  memberId                 Int
  chitFundId               Int
  balance                  Float     @default(0)
  balancePaymentDate       DateTime?
  balancePaymentStatus     String?   @default("Pending")
  actualBalancePaymentDate DateTime?
  notes                    String?
  createdAt                DateTime  @default(now())
  updatedAt                DateTime  @updatedAt
  chitFund                 ChitFund  @relation(fields: [chitFundId], references: [id])
  member                   Member    @relation(fields: [memberId], references: [id])

  @@index([chitFundId], map: "Contribution_chitFundId_fkey")
  @@index([memberId], map: "Contribution_memberId_fkey")
}

model Auction {
  id              Int      @id @default(autoincrement())
  chitFundId      Int
  month           Int
  date            DateTime
  winnerId        Int
  amount          Float
  lowestBid       Float?
  highestBid      Float?
  numberOfBidders Int?
  notes           String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  chitFund        ChitFund @relation(fields: [chitFundId], references: [id])
  winner          Member   @relation(fields: [winnerId], references: [id])

  @@index([chitFundId], map: "Auction_chitFundId_fkey")
  @@index([winnerId], map: "Auction_winnerId_fkey")
}

model ChitFundFixedAmount {
  id         Int      @id @default(autoincrement())
  chitFundId Int
  month      Int
  amount     Float
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  chitFund   ChitFund @relation(fields: [chitFundId], references: [id])

  @@unique([chitFundId, month])
  @@index([chitFundId], map: "ChitFundFixedAmount_chitFundId_fkey")
}

model Loan {
  id                Int               @id @default(autoincrement())
  borrowerId        Int
  loanType          String
  amount            Float
  interestRate      Float
  documentCharge    Float             @default(0)
  currentMonth      Int               @default(0)
  installmentAmount Float             @default(0)
  duration          Int
  disbursementDate  DateTime
  repaymentType     String
  remainingAmount   Float
  overdueAmount     Float             @default(0)
  missedPayments    Int               @default(0)
  nextPaymentDate   DateTime?
  status            String            @default("Active")
  purpose           String?
  createdAt         DateTime          @default(now())
  updatedAt         DateTime          @updatedAt
  createdById       Int
  borrower          GlobalMember      @relation("BorrowerToLoan", fields: [borrowerId], references: [id])
  createdBy         User              @relation(fields: [createdById], references: [id])
  paymentSchedules  PaymentSchedule[]
  repayments        Repayment[]

  @@index([borrowerId], map: "Loan_borrowerId_fkey")
  @@index([createdById], map: "Loan_createdById_fkey")
}

model Repayment {
  id          Int      @id @default(autoincrement())
  amount      Float
  paidDate    DateTime
  loanId      Int
  paymentType String   @default("full")
  period      Int
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  loan        Loan     @relation(fields: [loanId], references: [id])

  @@index([loanId], map: "Repayment_loanId_fkey")
}

model PaymentSchedule {
  id                Int       @id @default(autoincrement())
  loanId            Int
  period            Int
  dueDate           DateTime
  amount            Float
  status            String    @default("Pending")
  actualPaymentDate DateTime?
  notes             String?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  loan              Loan      @relation(fields: [loanId], references: [id])

  @@index([loanId], map: "PaymentSchedule_loanId_fkey")
}

model EmailLog {
  id          Int      @id @default(autoincrement())
  emailType   String   // "monthly" or "weekly"
  period      String   // "2024-01" for monthly, "2024-W01" for weekly
  sentDate    DateTime
  status      String   @default("sent") // "sent", "failed", "recovered"
  recipients  String   // JSON array of recipient emails
  fileName    String?  // Name of the attached file
  isRecovery  Boolean  @default(false) // True if this was a recovery email
  errorMessage String? // Error message if failed
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@unique([emailType, period])
  @@index([emailType, sentDate])
}
